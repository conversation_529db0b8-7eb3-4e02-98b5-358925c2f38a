FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["Cherish.Api/Cherish.Api.csproj", "Cherish.Api/"]
COPY ["Cherish.Core/Cherish.Core.csproj", "Cherish.Core/"]
COPY ["Cherish.Infrastructure/Cherish.Infrastructure.csproj", "Cherish.Infrastructure/"]
COPY ["Cherish.Persistence/Cherish.Persistence.csproj", "Cherish.Persistence/"]
RUN dotnet restore "Cherish.Api/Cherish.Api.csproj"
COPY . .
WORKDIR "/src/Cherish.Api"
RUN dotnet build "Cherish.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Cherish.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Cherish.Api.dll"]
